#!/bin/bash

# 测试插件兼容性脚本

echo "🔍 测试插件兼容性..."

# 停止当前服务
docker-compose -f docker-compose-clean.yml down

# 清理 addons 目录
rm -rf addons/*

echo "📦 测试阶段 1: 只使用核心 Odoo"
docker-compose -f docker-compose-clean.yml up -d
sleep 30

echo "🌐 测试访问 http://localhost:8073"
if curl -s http://localhost:8073 | grep -q "odoo"; then
    echo "✅ 核心 Odoo 工作正常"
else
    echo "❌ 核心 Odoo 有问题"
    exit 1
fi

# 停止服务
docker-compose -f docker-compose-clean.yml down
sleep 5

echo "📦 测试阶段 2: 添加 my-addons"
cp -r my-addons/* addons/ 2>/dev/null || echo "my-addons 为空或不存在"
docker-compose -f docker-compose-clean.yml up -d
sleep 30

echo "🌐 测试访问 http://localhost:8073"
if curl -s http://localhost:8073 | grep -q "odoo"; then
    echo "✅ my-addons 兼容"
else
    echo "❌ my-addons 有问题"
    # 清理并继续
    rm -rf addons/*
fi

# 停止服务
docker-compose -f docker-compose-clean.yml down
sleep 5

echo "📦 测试阶段 3: 添加 free-addons"
rm -rf addons/*
cp -r free-addons/* addons/ 2>/dev/null || echo "free-addons 为空或不存在"
docker-compose -f docker-compose-clean.yml up -d
sleep 30

echo "🌐 测试访问 http://localhost:8073"
if curl -s http://localhost:8073 | grep -q "odoo"; then
    echo "✅ free-addons 兼容"
else
    echo "❌ free-addons 有问题"
    rm -rf addons/*
fi

echo "🏁 测试完成"
