#!/bin/bash

# Hex ERP 启动脚本 - 多种配置选择

echo "🚀 Hex ERP 启动选择"
echo ""
echo "请选择启动模式："
echo "1. 干净的 Odoo 17 环境 (推荐，解决样式错误)"
echo "2. 包含所有插件的完整环境"
echo "3. 最小化环境"
echo "4. 停止所有服务"
echo "5. 查看服务状态"
echo ""

read -p "请选择 (1-5): " choice

case $choice in
    1)
        echo "🧹 启动干净的 Odoo 17 环境..."
        
        # 停止其他服务
        docker-compose -f docker-compose-simple.yml down 2>/dev/null
        docker-compose -f docker-compose-minimal.yml down 2>/dev/null
        
        # 启动干净环境
        docker-compose -f docker-compose-clean.yml up -d
        
        echo ""
        echo "✅ 服务已启动！"
        echo "🌐 访问地址: http://localhost:8073"
        echo "👤 数据库用户: odoo"
        echo "🔑 数据库密码: myodoo"
        echo ""
        echo "📋 管理命令:"
        echo "  查看日志: docker-compose -f docker-compose-clean.yml logs -f"
        echo "  停止服务: docker-compose -f docker-compose-clean.yml down"
        echo ""
        ;;
        
    2)
        echo "📦 启动完整环境 (包含所有插件)..."
        
        # 停止其他服务
        docker-compose -f docker-compose-clean.yml down 2>/dev/null
        docker-compose -f docker-compose-minimal.yml down 2>/dev/null
        
        # 启动完整环境
        docker-compose -f docker-compose-simple.yml up -d
        
        echo ""
        echo "✅ 服务已启动！"
        echo "🌐 访问地址: http://localhost:8071"
        echo "⚠️  如果遇到样式错误，请选择选项 1"
        echo ""
        ;;
        
    3)
        echo "🔧 启动最小化环境..."
        
        # 停止其他服务
        docker-compose -f docker-compose-clean.yml down 2>/dev/null
        docker-compose -f docker-compose-simple.yml down 2>/dev/null
        
        # 启动最小环境
        docker-compose -f docker-compose-minimal.yml up -d
        
        echo ""
        echo "✅ 服务已启动！"
        echo "🌐 访问地址: http://localhost:8072"
        echo ""
        ;;
        
    4)
        echo "🛑 停止所有服务..."
        
        docker-compose -f docker-compose-clean.yml down 2>/dev/null
        docker-compose -f docker-compose-simple.yml down 2>/dev/null
        docker-compose -f docker-compose-minimal.yml down 2>/dev/null
        
        echo "✅ 所有服务已停止"
        ;;
        
    5)
        echo "📊 服务状态:"
        echo ""
        
        echo "干净环境 (端口 8073):"
        docker-compose -f docker-compose-clean.yml ps 2>/dev/null || echo "未运行"
        
        echo ""
        echo "完整环境 (端口 8071):"
        docker-compose -f docker-compose-simple.yml ps 2>/dev/null || echo "未运行"
        
        echo ""
        echo "最小环境 (端口 8072):"
        docker-compose -f docker-compose-minimal.yml ps 2>/dev/null || echo "未运行"
        ;;
        
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
