#!/bin/bash

# Hex ERP 快速启动脚本

echo "🚀 Hex ERP 快速启动"

# 检查 Docker
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 创建目录
mkdir -p logs filestore
chmod 755 logs filestore

echo "📦 启动 Hex ERP 服务..."

# 启动服务
docker-compose -f docker-compose-simple.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
if docker-compose -f docker-compose-simple.yml ps | grep -q "Up"; then
    echo "✅ 服务启动成功！"
    echo ""
    echo "🌐 访问地址: http://localhost:8071"
    echo "👤 管理员密码: admin123"
    echo ""
    echo "📋 常用命令:"
    echo "  查看日志: docker-compose -f docker-compose-simple.yml logs -f"
    echo "  停止服务: docker-compose -f docker-compose-simple.yml down"
    echo "  重启服务: docker-compose -f docker-compose-simple.yml restart"
    echo ""
    echo "🔧 管理脚本: ./docker-start.sh"
else
    echo "❌ 服务启动失败，请检查日志:"
    docker-compose -f docker-compose-simple.yml logs
fi
