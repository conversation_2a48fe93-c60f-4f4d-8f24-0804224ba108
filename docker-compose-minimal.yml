version: '3.8'

services:
  # PostgreSQL 数据库
  db:
    image: postgres:14
    container_name: hex_erp_db_minimal
    environment:
      POSTGRES_DB: hex_erp_minimal
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
    volumes:
      - postgres_data_minimal:/var/lib/postgresql/data
    ports:
      - '5434:5432'
    restart: unless-stopped

  # Odoo 17 应用 - 最小配置，不加载自定义插件
  odoo:
    image: odoo:17.0
    container_name: hex_erp_odoo_minimal
    depends_on:
      - db
    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=odoo
    ports:
      - '8072:8069'
    volumes:
      # 映射配置文件
      - ./odoo-minimal.conf:/etc/odoo/odoo.conf
      # 只映射数据目录，不映射插件
      - ./filestore:/var/lib/odoo/filestore
      - ./logs:/var/log/odoo
    restart: unless-stopped
    command: odoo -c /etc/odoo/odoo.conf -i base

volumes:
  postgres_data_minimal:
