services:
  db:
    image: postgres:15
    user: root
    environment:
      - POSTGRES_PASSWORD=myodoo
      - POSTGRES_USER=odoo
      - POSTGRES_DB=postgres
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - odoo-db-data:/var/lib/postgresql/data/pgdata
    ports:
      - "5435:5432"

  odoo:
    image: odoo:17.0
    user: root
    depends_on:
      - db
    ports:
      - "8073:8069"
    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=myodoo
    volumes:
      - odoo-web-data:/var/lib/odoo
      - ./config:/etc/odoo
      - ./addons:/mnt/extra-addons
      - ./logs:/var/log/odoo

volumes:
  odoo-web-data:
  odoo-db-data:
